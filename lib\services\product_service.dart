import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/product_model.dart';

class ProductService extends ChangeNotifier {
  final List<Product> _products = [];
  bool _isLoading = false;
  String? _error;

  List<Product> get products => _products;
  bool get isLoading => _isLoading;
  String? get error => _error;

  final _supabase = Supabase.instance.client;

  // Initialize by loading real products from Supabase
  ProductService() {
    _loadProductsFromSupabase();
  }

  // Load products from Supabase with real-time subscription
  Future<void> _loadProductsFromSupabase() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Load initial products
      final response = await _supabase
          .from('products')
          .select('''
            *,
            users!products_seller_id_fkey(name, profile_image)
          ''')
          .order('created_at', ascending: false);

      _products.clear();

      for (final productData in response) {
        try {
          final product = Product.fromJson(productData);
          _products.add(product);
        } catch (e) {
          debugPrint('Error parsing product: $e');
          // Continue with other products even if one fails
        }
      }

      _isLoading = false;
      notifyListeners();

      // Set up real-time subscription for new products
      _setupRealtimeSubscription();
    } catch (e) {
      _error = 'Failed to load products: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error loading products from Supabase: $e');

      // Load dummy data as fallback
      _loadDummyDataAsFallback();
    }
  }

  // Set up real-time subscription for product changes
  void _setupRealtimeSubscription() {
    _supabase
        .channel('products_channel')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'products',
          callback: (payload) {
            try {
              final newProduct = Product.fromJson(payload.newRecord);
              _products.insert(
                0,
                newProduct,
              ); // Add to beginning (newest first)
              notifyListeners();
              debugPrint('New product added via real-time: ${newProduct.name}');
            } catch (e) {
              debugPrint('Error processing real-time product insert: $e');
            }
          },
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'products',
          callback: (payload) {
            try {
              final updatedProduct = Product.fromJson(payload.newRecord);
              final index = _products.indexWhere(
                (p) => p.id == updatedProduct.id,
              );
              if (index != -1) {
                _products[index] = updatedProduct;
                notifyListeners();
                debugPrint(
                  'Product updated via real-time: ${updatedProduct.name}',
                );
              }
            } catch (e) {
              debugPrint('Error processing real-time product update: $e');
            }
          },
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.delete,
          schema: 'public',
          table: 'products',
          callback: (payload) {
            try {
              final deletedId = payload.oldRecord['id'].toString();
              _products.removeWhere((p) => p.id == deletedId);
              notifyListeners();
              debugPrint('Product deleted via real-time: $deletedId');
            } catch (e) {
              debugPrint('Error processing real-time product delete: $e');
            }
          },
        )
        .subscribe();
  }

  // Fallback dummy data in case Supabase fails
  void _loadDummyDataAsFallback() {
    _products.addAll([
      Product(
        id: 'dummy_1',
        name: 'Moroccan Ceramic Plate',
        description:
            'Handcrafted ceramic plate with traditional Moroccan patterns.',
        price: 250.0,
        imageUrl:
            'https://images.unsplash.com/photo-1603006905393-c279c4320be5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        category: 'Home Decor',
        sellerId: 'dummy_seller',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        sellerName: 'Demo Seller',
        sellerImage: 'https://randomuser.me/api/portraits/women/1.jpg',
      ),
      Product(
        id: 'dummy_2',
        name: 'Argan Oil (100ml)',
        description: 'Pure Moroccan Argan oil for hair and skin care.',
        price: 120.0,
        imageUrl:
            'https://images.unsplash.com/photo-1608571423902-eed4a5ad8108?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        category: 'Beauty',
        sellerId: 'dummy_seller',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        sellerName: 'Demo Seller',
        sellerImage: 'https://randomuser.me/api/portraits/women/1.jpg',
      ),
    ]);
    notifyListeners();
  }

  // Get products by seller ID
  List<Product> getProductsBySellerId(String sellerId) {
    return _products.where((product) => product.sellerId == sellerId).toList();
  }

  // Get products by category
  List<Product> getProductsByCategory(String category) {
    return _products.where((product) => product.category == category).toList();
  }

  // Search products
  List<Product> searchProducts(String query) {
    return _products
        .where(
          (product) =>
              product.name.toLowerCase().contains(query.toLowerCase()) ||
              product.description.toLowerCase().contains(query.toLowerCase()) ||
              product.category.toLowerCase().contains(query.toLowerCase()) ||
              (product.sellerName?.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ??
                  false),
        )
        .toList();
  }

  // Add a new product
  Future<Product> addProduct(Product product) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User must be logged in to add a product');
      }

      // Ensure seller_id is not null and use Supabase auth user ID
      final sellerId = user.id;
      if (sellerId.isEmpty) {
        throw Exception('Invalid user ID from Supabase auth');
      }

      debugPrint('Adding product for seller ID: $sellerId');
      debugPrint('Product data: ${product.toJson()}');

      // Get the current user's store_id for linking the product
      final userResponse =
          await _supabase
              .from('users')
              .select('store_id')
              .eq('id', sellerId)
              .single();

      final String? storeId = userResponse['store_id'];

      // Insert into Supabase and get the created product
      final response =
          await _supabase
              .from('products')
              .insert({
                'name': product.name,
                'description': product.description,
                'price': product.price,
                'image_url': product.imageUrl,
                'category': product.category,
                'seller_id': sellerId, // Use Supabase auth user ID directly
                'store_id': storeId, // Link to the seller's store
              })
              .select('''
            *,
            users!products_seller_id_fkey(name, profile_image)
          ''')
              .single();

      debugPrint('Supabase insert response: $response');

      // Create the product object from the response using fromJson
      final createdProduct = Product.fromJson(response);

      // Add to local state at the beginning (newest first)
      _products.insert(0, createdProduct);

      _isLoading = false;
      notifyListeners();

      debugPrint('Product added successfully: ${createdProduct.name}');
      return createdProduct;
    } catch (e) {
      // Enhanced error logging with detailed Supabase error information
      String detailedError = 'Failed to add product';

      if (e is PostgrestException) {
        detailedError = 'Supabase Database Error: ${e.message}';
        debugPrint('PostgrestException Details:');
        debugPrint('  Message: ${e.message}');
        debugPrint('  Code: ${e.code}');
        debugPrint('  Details: ${e.details}');
        debugPrint('  Hint: ${e.hint}');
      } else if (e is AuthException) {
        detailedError = 'Authentication Error: ${e.message}';
        debugPrint('AuthException Details:');
        debugPrint('  Message: ${e.message}');
      } else {
        detailedError = 'Unexpected Error: $e';
        debugPrint('General Exception: $e');
        debugPrint('Exception Type: ${e.runtimeType}');
      }

      _error = detailedError;
      _isLoading = false;
      notifyListeners();

      debugPrint('=== DETAILED ERROR INFORMATION ===');
      debugPrint('Error Type: ${e.runtimeType}');
      debugPrint('Error Message: $e');
      debugPrint('Stack Trace: ${StackTrace.current}');
      debugPrint('================================');

      rethrow;
    }
  }

  // Refresh products from Supabase
  Future<void> refreshProducts() async {
    await _loadProductsFromSupabase();
  }

  // Update a product
  Future<void> updateProduct(Product updatedProduct) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _supabase
          .from('products')
          .update({
            'name': updatedProduct.name,
            'description': updatedProduct.description,
            'price': updatedProduct.price,
            'image_url': updatedProduct.imageUrl,
            'category': updatedProduct.category,
          })
          .eq('id', updatedProduct.id);

      final index = _products.indexWhere(
        (product) => product.id == updatedProduct.id,
      );
      if (index != -1) {
        _products[index] = updatedProduct;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to update product: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error updating product: $e');
      rethrow;
    }
  }

  // Delete a product
  Future<void> deleteProduct(String productId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _supabase.from('products').delete().eq('id', productId);

      _products.removeWhere((product) => product.id == productId);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to delete product: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error deleting product: $e');
      rethrow;
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Dispose method to clean up subscriptions
  @override
  void dispose() {
    // Unsubscribe from real-time channel
    _supabase.removeAllChannels();
    super.dispose();
  }
}
