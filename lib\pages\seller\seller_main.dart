import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../services/auth_service.dart';
import '../../services/store_service.dart';
import '../../services/preferences_service.dart';
import '../../widgets/curved_bottom_nav_bar.dart';
import '../../widgets/welcome_notification.dart';
import '../../theme/app_colors.dart';
import 'products_page.dart';
import 'orders_page.dart';
import 'seller_chat_page.dart';
import 'live_page.dart';
import 'account_page.dart';
import 'create_store_page.dart';

class SellerMainPage extends StatefulWidget {
  const SellerMainPage({super.key});

  @override
  State<SellerMainPage> createState() => _SellerMainPageState();
}

class _SellerMainPageState extends State<SellerMainPage> {
  int _currentIndex = 0;
  bool _showWelcomeNotification = false;

  @override
  void initState() {
    super.initState();
    _checkWelcomeStatus();
  }

  Future<void> _checkWelcomeStatus() async {
    final hasSeenWelcome = await PreferencesService.getHasSeenWelcome();

    if (!hasSeenWelcome) {
      if (mounted) {
        setState(() {
          _showWelcomeNotification = true;
        });

        // Auto-hide welcome notification after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _showWelcomeNotification = false;
            });
          }
        });

        // Save that user has seen welcome message
        await PreferencesService.setHasSeenWelcome();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthService, StoreService>(
      builder: (context, authService, storeService, _) {
        final user = authService.currentUser;

        // Check if the seller has store info
        if (user != null &&
            user.userType == UserType.seller &&
            (user.storeName == null ||
                user.storeName!.isEmpty ||
                user.storeId == null)) {
          // If no store info, show store creation page
          return const CreateStorePage();
        }

        // If has store, show main seller interface
        final List<Widget> pages = [
          const ProductsPage(),
          const SellerOrdersPage(),
          const SellerChatPage(),
          const SellerLivePage(),
          const SellerAccountPage(),
        ];

        return Scaffold(
          body: Stack(
            children: [
              // Main content
              pages[_currentIndex],

              // Welcome notification
              if (_showWelcomeNotification)
                Positioned(
                  top: MediaQuery.of(context).padding.top + 16,
                  left: 16,
                  right: 16,
                  child: WelcomeNotification(
                    message:
                        'Welcome back to HadiHia, Seller! Manage your store and products here.',
                    backgroundColor: AppColors.secondary,
                    onDismiss: () {
                      setState(() {
                        _showWelcomeNotification = false;
                      });
                    },
                  ),
                ),
            ],
          ),
          bottomNavigationBar: SellerCurvedBottomNavBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
          ),
        );
      },
    );
  }
}
