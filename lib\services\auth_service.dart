import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart' as myUser;
import '../constants/app_constants.dart';

class AuthService extends ChangeNotifier {
  myUser.User? _currentUser;
  bool _isLoading = false;

  myUser.User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _currentUser != null;

  final _supabase = Supabase.instance.client;

  Future<bool> register(
    String name,
    String email,
    String password,
    myUser.UserType userType,
  ) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Signup failed');
      }

      final userId = response.user!.id;

      // إضافة بيانات المستخدم في جدول مخصص
      await _supabase.from('users').insert({
        'id': userId,
        'name': name,
        'email': email,
        'profile_image':
            userType == myUser.UserType.customer
                ? 'https://randomuser.me/api/portraits/men/2.jpg'
                : 'https://randomuser.me/api/portraits/women/2.jpg',
        'user_type':
            userType == myUser.UserType.customer ? 'customer' : 'seller',
        'wallet_balance': 0.0,
        'store_id': null,
      });

      _currentUser = myUser.User(
        id: userId,
        name: name,
        email: email,
        profileImage:
            userType == myUser.UserType.customer
                ? 'https://randomuser.me/api/portraits/men/2.jpg'
                : 'https://randomuser.me/api/portraits/women/2.jpg',
        userType: userType,
        walletBalance: 0.0,
        createdAt: DateTime.now(),
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      print('Registration Error: $e');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> login(String email, String password) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Login failed');
      }

      final userId = response.user!.id;

      final userData =
          await _supabase.from('users').select().eq('id', userId).single();

      _currentUser = myUser.User(
        id: userId,
        name: userData['name'],
        email: userData['email'],
        profileImage: userData['profile_image'],
        userType:
            userData['user_type'] == 'seller'
                ? myUser.UserType.seller
                : myUser.UserType.customer,
        walletBalance: (userData['wallet_balance'] ?? 0).toDouble(),
        storeId: userData['store_id'],
        storeName: userData['store_name'],
        storeDescription: userData['store_description'],
        storeImageUrl: userData['store_image_url'],
        createdAt:
            userData['created_at'] != null
                ? DateTime.parse(userData['created_at'])
                : DateTime.now(),
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      print('Login Error: $e');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    await _supabase.auth.signOut();
    _currentUser = null;
    notifyListeners();
  }

  Future<void> updateProfile({String? name, String? profileImage}) async {
    if (_currentUser == null) return;

    final updates = {
      if (name != null) 'name': name,
      if (profileImage != null) 'profile_image': profileImage,
    };

    await _supabase.from('users').update(updates).eq('id', _currentUser!.id);

    _currentUser = _currentUser!.copyWith(
      name: name ?? _currentUser!.name,
      profileImage: profileImage ?? _currentUser!.profileImage,
    );
    notifyListeners();
  }

  void updateStoreId(String storeId) {
    if (_currentUser == null ||
        _currentUser!.userType != myUser.UserType.seller) {
      return;
    }

    _currentUser = _currentUser!.copyWith(storeId: storeId);
    notifyListeners();
  }

  Future<void> updateStoreInfo({
    required String storeName,
    required String storeDescription,
    required String storeImageUrl,
  }) async {
    if (_currentUser == null) {
      throw Exception('No user logged in');
    }

    try {
      // Use default image if no image provided
      final String finalImageUrl =
          storeImageUrl.isEmpty
              ? AppConstants.defaultStoreImageUrl
              : storeImageUrl;

      // Update user in Supabase
      await _supabase
          .from('users')
          .update({
            'store_name': storeName,
            'store_description': storeDescription,
            'store_image_url': finalImageUrl,
          })
          .eq('id', _currentUser!.id);

      // Update local user object
      _currentUser = _currentUser!.copyWith(
        storeName: storeName,
        storeDescription: storeDescription,
        storeImageUrl: finalImageUrl,
      );

      notifyListeners();
    } catch (e) {
      debugPrint('Error updating store info: $e');
      rethrow;
    }
  }
}
