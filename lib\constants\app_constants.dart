class AppConstants {
  // Default store image URL - from Supabase storage
  static const String defaultStoreImageUrl =
      'https://vwryhuiffrwpplvylbfb.supabase.co/storage/v1/object/public/defaultstore/default.png';

  // Store image recommendations
  static const String storeImageRecommendations = '''
Recommended image specifications:
• Dimensions: 600x600 pixels
• Format: JPG or PNG
• Max file size: 2MB
• High quality, clear branding''';

  // Storage bucket names - using defaultstore for both store and product images
  static const String storeImageBucket = 'defaultstore';
  static const String productImageBucket = 'defaultstore';

  // Image size limits
  static const int maxStoreImageSizeBytes = 2 * 1024 * 1024; // 2MB
  static const int maxProductImageSizeBytes = 5 * 1024 * 1024; // 5MB

  // Recommended dimensions
  static const int recommendedStoreImageSize = 600;
  static const double storeImageAspectRatio = 1.0; // 1:1 square
}
