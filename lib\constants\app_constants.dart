class AppConstants {
  // Default store image URL - a professional store placeholder
  static const String defaultStoreImageUrl = 
      'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600&q=80';
  
  // Store image recommendations
  static const String storeImageRecommendations = '''
Recommended image specifications:
• Dimensions: 600x600 pixels or higher
• Aspect ratio: 1:1 (square)
• Format: JPG or PNG
• Max file size: 2MB
• High quality, clear branding''';

  // Storage bucket names
  static const String storeImageBucket = 'storeimage';
  static const String productImageBucket = 'productimages';
  
  // Image size limits
  static const int maxStoreImageSizeBytes = 2 * 1024 * 1024; // 2MB
  static const int maxProductImageSizeBytes = 5 * 1024 * 1024; // 5MB
  
  // Recommended dimensions
  static const int recommendedStoreImageSize = 600;
  static const double storeImageAspectRatio = 1.0; // 1:1 square
}
