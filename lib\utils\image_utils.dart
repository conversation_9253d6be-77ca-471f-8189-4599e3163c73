import '../constants/app_constants.dart';

class ImageUtils {
  /// Get store image URL with fallback to default
  static String getStoreImageUrl(String? storeImageUrl) {
    if (storeImageUrl == null || storeImageUrl.isEmpty) {
      return AppConstants.defaultStoreImageUrl;
    }
    return storeImageUrl;
  }

  /// Check if the image URL is the default store image
  static bool isDefaultStoreImage(String? imageUrl) {
    return imageUrl == null || 
           imageUrl.isEmpty || 
           imageUrl == AppConstants.defaultStoreImageUrl;
  }

  /// Get a placeholder image URL for stores
  static String getStorePlaceholderUrl() {
    return AppConstants.defaultStoreImageUrl;
  }
}
